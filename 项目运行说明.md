# 极光小车视觉识别项目运行说明

## 项目概述

这是一个基于MaixCAM的智能小车视觉识别项目，主要功能是通过摄像头实时检测矩形轨道和紫色激光点，并通过串口将识别结果发送给下位机控制小车运动。

## 项目结构

```
jiguang_car/
├── main.py                 # 主程序文件
├── app.yaml               # 应用配置文件
├── app.png                # 应用图标
├── dist/                  # 打包输出目录
│   ├── maix-jgcar-v1.0.0.zip
│   └── maix-jgcar-v1.0.1.zip
└── micu_uart_lib/         # 串口通信库
    ├── __init__.py        # 库初始化文件
    ├── config.py          # 配置管理
    ├── simple_uart.py     # 串口通信核心
    └── utils.py           # 工具函数
```

## 核心功能模块

### 1. 紫色激光检测器 (PurpleLaserDetector)
- **功能**: 检测图像中的紫色激光点
- **原理**: 使用HSV颜色空间过滤紫色区域，通过形态学操作去噪，提取轮廓中心点
- **输出**: 激光点坐标列表

### 2. 矩形轨道检测
- **功能**: 检测图像中的矩形轨道框架
- **原理**: 
  - 图像二值化处理
  - 轮廓检测和面积筛选
  - 多边形近似识别四边形
  - 按面积排序区分外框和内框

### 3. 透视变换与圆形轨迹生成
- **功能**: 在检测到的矩形内框中生成标准圆形轨迹
- **原理**:
  - 对内框进行透视变换校正为标准矩形
  - 在校正后的矩形中心生成圆形轨迹点
  - 将轨迹点逆变换映射回原图坐标系

### 4. 串口通信
- **功能**: 将检测结果通过串口发送给下位机
- **协议**: 使用自定义帧格式 `$$数据##`
- **数据格式**:
  - 圆形轨迹: `C,点数量,x1,y1,x2,y2,...`
  - 激光点: `L,点数量,x1,y1,x2,y2,...`

## 运行步骤详解

### 第一步：系统初始化
1. **显示器初始化**: 创建320x240分辨率的显示对象
2. **摄像头初始化**: 配置BGR888格式的摄像头
3. **串口初始化**: 
   - 设备: `/dev/ttyS0`
   - 波特率: `115200`
   - 帧格式: `$$数据##`

### 第二步：图像采集与预处理
1. **图像读取**: 从摄像头获取实时图像
2. **格式转换**: 将MaixPy图像格式转换为OpenCV格式
3. **灰度转换**: 转换为灰度图像用于轮廓检测

### 第三步：矩形轨道检测
1. **二值化处理**: 阈值设为46，提取轮廓特征
2. **轮廓检测**: 使用`cv2.findContours`检测所有轮廓
3. **面积筛选**: 保留面积在3000-40000像素范围内的轮廓
4. **形状识别**: 使用多边形近似检测四边形
5. **分类排序**: 按面积排序，区分外框和内框

### 第四步：透视变换与轨迹生成
1. **透视变换计算**: 
   - 将检测到的四边形校正为200x150的标准矩形
   - 计算变换矩阵M和逆矩阵M_inv
2. **圆形轨迹生成**:
   - 在校正后矩形中心生成半径40像素的圆
   - 圆周均匀分布12个轨迹点
3. **坐标映射**: 将轨迹点通过逆变换映射回原图坐标系

### 第五步：激光点检测
1. **颜色空间转换**: BGR转HSV
2. **紫色区域提取**: HSV范围[130,80,80]到[160,255,255]
3. **形态学处理**: 使用3x3核进行闭运算去噪
4. **中心点提取**: 计算轮廓的最小外接矩形中心

### 第六步：数据输出
1. **串口数据发送**:
   - 圆形轨迹数据: `C,12,x1,y1,x2,y2,...,x12,y12`
   - 激光点数据: `L,1,x,y`
2. **图像显示**:
   - 绘制检测到的矩形轮廓（绿色）
   - 显示圆形轨迹点（红色）
   - 标记激光点位置（紫色）

### 第七步：循环执行
程序在主循环中持续执行上述步骤，直到接收到退出信号。

## 关键参数配置

| 参数名称 | 数值 | 说明 |
|---------|------|------|
| min_contour_area | 3000 | 最小轮廓面积 |
| max_contour_area | 40000 | 最大轮廓面积 |
| target_sides | 4 | 目标多边形边数 |
| corrected_width | 200 | 校正后矩形宽度 |
| corrected_height | 150 | 校正后矩形高度 |
| circle_radius | 40 | 圆形轨迹半径 |
| circle_num_points | 12 | 圆周轨迹点数量 |
| binary_threshold | 46 | 二值化阈值 |

## 输出产物

### 1. 实时视频流
- 显示原始摄像头画面
- 叠加检测结果的可视化标记
- 实时更新检测状态

### 2. 串口数据流
- **圆形轨迹数据**: 为小车提供标准行驶路径
- **激光点数据**: 为小车提供目标跟踪信息
- **数据格式**: 结构化的坐标信息，便于下位机解析

### 3. 调试信息
- 串口初始化状态
- 帧格式配置信息
- 数据发送确认信息

## 应用场景

1. **自动驾驶小车**: 沿着检测到的矩形轨道行驶
2. **激光跟踪**: 追踪紫色激光指示的目标点
3. **路径规划**: 基于视觉识别的智能导航
4. **教育演示**: 计算机视觉和机器人控制的教学项目

## 技术特点

- **实时性**: 基于摄像头的实时图像处理
- **准确性**: 多重滤波和形态学处理提高检测精度
- **稳定性**: 透视变换确保轨迹的几何一致性
- **可扩展性**: 模块化设计便于功能扩展
